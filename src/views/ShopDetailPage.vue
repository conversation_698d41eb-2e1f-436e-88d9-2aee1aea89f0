<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button v-if="isPreviewMode" @click="exitPreview" class="preview-exit">
            <ion-icon :icon="closeCircleOutline" slot="start"></ion-icon>
            退出預覽
          </ion-button>
          <ion-back-button v-else default-href="/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ shop?.name || '商店' }}</ion-title>
        <ion-buttons slot="end" v-if="isOwner">
          <ion-button @click="previewShop" title="預覽商店">
            <ion-icon :icon="eyeOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button :router-link="`/shops/${shop.id}/edit-profile`" title="編輯詳細檔案">
            <ion-icon :icon="documentTextOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button @click="showEditModal = true" title="快速編輯">
            <ion-icon :icon="createOutline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="shop" class="shop-container">
        <!-- Shop Banner -->
        <div class="shop-banner">
          <img :src="shop.banner || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8'" :alt="shop.name">
        </div>

        <!-- Shop Info Section -->
        <div class="shop-info-section">
          <div class="shop-header">
            <div class="shop-logo">
              <img :src="shop.logo || 'https://images.unsplash.com/photo-1516876437184-593fda40c7ce'" :alt="shop.name">
            </div>

            <div class="shop-title-area">
              <h1 class="shop-name">{{ shop.name }}</h1>
              <div class="shop-actions-container">
                <div class="shop-rating">
                  <ion-icon :icon="star" color="warning"></ion-icon>
                  <span>{{ shop.rating ? shop.rating.toFixed(1) : '0.0' }} ({{ shop.rating_count || 0 }})</span>
                </div>
                <ion-button
                  v-if="!isOwner && authStore.isAuthenticated"
                  fill="clear"
                  size="small"
                  class="rate-button"
                  @click="openRatingModal"
                >
                  {{ userHasRated ? '修改評分' : '評分' }}
                </ion-button>
                <ion-button
                  v-if="!isOwner && authStore.isAuthenticated"
                  fill="clear"
                  size="small"
                  class="like-button"
                  @click="toggleFavorite"
                >
                  <ion-icon :icon="isShopLiked ? heart : heartOutline" />
                  {{ isShopLiked ? '已收藏' : '收藏' }}
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Shop Category & Metrics Section -->
          <div class="shop-metrics-section">
            <!-- Shop Category -->
            <div v-if="shopCategory" class="shop-category">
              <ion-icon :icon="pricetag"></ion-icon>
              <span>{{ shopCategory.title }}</span>
            </div>

            <div class="shop-metrics">
              <div class="metric-item">
                <ion-icon :icon="cube"></ion-icon>
                <span>{{ shop.product_count || 0 }} 產品</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-item">
                <ion-icon :icon="heart"></ion-icon>
                <span>{{ shop.like_count || 0 }} 收藏</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-item">
                <ion-icon :icon="time"></ion-icon>
                <span>{{ formatJoinDate(shop.created_at) }}</span>
              </div>
            </div>
          </div>

          <!-- Shop Owner Information -->
          <div class="shop-owner-section">
            <ion-item lines="none" class="shop-owner-item" button @click="navigateToUserProfile(shop.owner_id)">
              <ion-icon :icon="personOutline" slot="start" class="owner-icon"></ion-icon>
              <ion-label>
                <h3>店主</h3>
                <h2>{{ shop.owner?.full_name || '未知' }}</h2>
              </ion-label>
              <ion-icon :icon="chevronForwardOutline" slot="end" color="medium"></ion-icon>
            </ion-item>
          </div>

          <!-- Website Information -->
          <div v-if="shop.website" class="shop-website-section">
            <ion-item lines="none" class="shop-website-item" button @click="openWebsite(shop.website)">
              <ion-icon :icon="globeOutline" slot="start" class="website-icon"></ion-icon>
              <ion-label>
                <h3>網站</h3>
                <h2>{{ formatWebsiteUrl(shop.website) }}</h2>
              </ion-label>
              <ion-icon :icon="openOutline" slot="end" color="medium"></ion-icon>
            </ion-item>
          </div>

          <!-- Shop Profile Tabs Section -->
          <div class="shop-profile-section">
            <div class="section-header">
              <h3 v-if="hasProfileData || shop.description">商家檔案</h3>
              <ion-button
                v-if="isOwner"
                :router-link="`/shops/${shop.id}/edit-profile`"
                fill="outline"
                size="small"
                class="edit-profile-btn"
              >
                <ion-icon :icon="hasProfileData ? documentTextOutline : addOutline" slot="start"></ion-icon>
                {{ hasProfileData ? '編輯檔案' : '建立檔案' }}
              </ion-button>
            </div>

            <div v-if="hasProfileData || shop.description" class="profile-tabs-container">
              <ion-segment v-model="selectedProfileTab" class="profile-segment">
                <ion-segment-button value="description">
                  <ion-icon :icon="documentTextOutline"></ion-icon>
                  <ion-label>商店介紹</ion-label>
                </ion-segment-button>
                <ion-segment-button value="business">
                  <ion-icon :icon="businessOutline"></ion-icon>
                  <ion-label>業務資訊</ion-label>
                </ion-segment-button>
                <ion-segment-button value="products">
                  <ion-icon :icon="cubeOutline"></ion-icon>
                  <ion-label>產品服務</ion-label>
                </ion-segment-button>
                <ion-segment-button value="contact">
                  <ion-icon :icon="callOutline"></ion-icon>
                  <ion-label>聯絡方式</ion-label>
                </ion-segment-button>
                <ion-segment-button value="more">
                  <ion-icon :icon="ellipsisHorizontalOutline"></ion-icon>
                  <ion-label>更多</ion-label>
                </ion-segment-button>
              </ion-segment>

              <!-- Tab Content -->
              <div class="tab-content">
                <!-- Description Tab -->
                <div v-show="selectedProfileTab === 'description'" class="tab-panel">
                  <div class="description-content">
                    <p v-if="shop.description" class="description-text">{{ shop.description }}</p>
                    <p v-else class="no-description">暫無商店介紹</p>
                  </div>
                </div>

                <!-- Business Info Tab -->
                <div v-show="selectedProfileTab === 'business'" class="tab-panel">
                  <div class="profile-grid-compact">
                    <div v-if="shop.business_type" class="profile-item-compact">
                      <span class="label">業務類型</span>
                      <span class="value">{{ shop.business_type }}</span>
                    </div>
                    <div v-if="shop.service_area" class="profile-item-compact">
                      <span class="label">服務範圍</span>
                      <span class="value">{{ shop.service_area }}</span>
                    </div>
                    <div v-if="shop.business_years" class="profile-item-compact">
                      <span class="label">業務年限</span>
                      <span class="value">{{ shop.business_years }}</span>
                    </div>
                    <div v-if="shop.operating_hours" class="profile-item-compact">
                      <span class="label">營業時間</span>
                      <span class="value">{{ shop.operating_hours }}</span>
                    </div>
                  </div>
                </div>

                <!-- Products & Services Tab -->
                <div v-show="selectedProfileTab === 'products'" class="tab-panel">
                  <div v-if="shop.main_products_services?.length" class="products-section">
                    <h4>主要產品服務</h4>
                    <div class="tags-container-compact">
                      <ion-chip v-for="service in shop.main_products_services" :key="service" color="primary" outline>
                        {{ service }}
                      </ion-chip>
                    </div>
                  </div>
                  <div v-if="shop.keywords?.length" class="keywords-section">
                    <h4>關鍵字標籤</h4>
                    <div class="tags-container-compact">
                      <ion-chip v-for="keyword in shop.keywords" :key="keyword" color="success" outline>
                        {{ keyword }}
                      </ion-chip>
                    </div>
                  </div>
                </div>

                <!-- Contact Tab -->
                <div v-show="selectedProfileTab === 'contact'" class="tab-panel">
                  <div class="contact-info-compact">
                    <div v-if="shop.contact_phone" class="contact-item-compact">
                      <ion-icon :icon="callOutline"></ion-icon>
                      <span>{{ shop.contact_phone }}</span>
                    </div>
                    <div v-if="shop.contact_email" class="contact-item-compact">
                      <ion-icon :icon="mailOutline"></ion-icon>
                      <span>{{ shop.contact_email }}</span>
                    </div>
                    <div v-if="shop.physical_address" class="contact-item-compact">
                      <ion-icon :icon="locationOutline"></ion-icon>
                      <span>{{ shop.physical_address }}</span>
                    </div>
                  </div>
                </div>

                <!-- More Tab -->
                <div v-show="selectedProfileTab === 'more'" class="tab-panel">
                  <div v-if="shop.languages_supported?.length || shop.payment_methods?.length" class="support-info">
                    <div v-if="shop.languages_supported?.length" class="support-section-compact">
                      <h4>語言支援</h4>
                      <div class="tags-container-compact">
                        <ion-chip v-for="lang in shop.languages_supported" :key="lang" color="secondary" outline>
                          {{ lang }}
                        </ion-chip>
                      </div>
                    </div>
                    <div v-if="shop.payment_methods?.length" class="support-section-compact">
                      <h4>支付方式</h4>
                      <div class="tags-container-compact">
                        <ion-chip v-for="method in shop.payment_methods" :key="method" color="tertiary" outline>
                          {{ method }}
                        </ion-chip>
                      </div>
                    </div>
                  </div>

                  <!-- Social Media -->
                  <div v-if="shop.social_media_links && Object.keys(shop.social_media_links).length" class="social-section">
                    <h4>社交媒體</h4>
                    <div class="social-links-compact">
                      <ion-button
                        v-if="shop.social_media_links.facebook"
                        fill="outline"
                        size="small"
                        @click="openLink(shop.social_media_links.facebook)"
                      >
                        <ion-icon :icon="logoFacebook" slot="start"></ion-icon>
                        Facebook
                      </ion-button>
                      <ion-button
                        v-if="shop.social_media_links.instagram"
                        fill="outline"
                        size="small"
                        @click="openLink(shop.social_media_links.instagram)"
                      >
                        <ion-icon :icon="logoInstagram" slot="start"></ion-icon>
                        Instagram
                      </ion-button>
                      <ion-button
                        v-if="shop.social_media_links.linkedin"
                        fill="outline"
                        size="small"
                        @click="openLink(shop.social_media_links.linkedin)"
                      >
                        <ion-icon :icon="logoLinkedin" slot="start"></ion-icon>
                        LinkedIn
                      </ion-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State for Profile -->
            <div v-else class="profile-empty-state-compact">
              <ion-icon :icon="documentTextOutline" color="medium"></ion-icon>
              <p v-if="isOwner">點擊上方按鈕建立詳細商家檔案</p>
              <p v-else>此商家尚未完善檔案資料</p>
            </div>
          </div>
        </div>



        <!-- Products Section -->
        <div class="products-section">
          <div class="section-header">
            <h2>{{ isOwner ? '我的產品' : '商店產品' }}</h2>
            <ion-button
              v-if="isOwner"
              :router-link="`/shops/${shop.id}/products/new`"
              fill="clear"
              class="add-product-btn"
            >
              <ion-icon :icon="addOutline" slot="start"></ion-icon>
              新增產品
            </ion-button>
          </div>

          <!-- Product Management Controls (Owner Only) -->
          <div v-if="isOwner && products.length > 0" class="product-controls">
            <!-- Mode Toggle -->
            <div class="mode-toggle">
              <ion-segment v-model="productManagementMode" @ionChange="handleModeChange">
                <ion-segment-button value="manage">
                  <ion-label>管理模式</ion-label>
                  <ion-icon :icon="searchOutline"></ion-icon>
                </ion-segment-button>
                <ion-segment-button value="reorder">
                  <ion-label>排序模式</ion-label>
                  <ion-icon :icon="reorderThreeOutline"></ion-icon>
                </ion-segment-button>
              </ion-segment>
            </div>

            <!-- Search and Filters (Management Mode Only) -->
            <div v-if="productManagementMode === 'manage'" class="search-filters">
              <ion-searchbar
                v-model="productSearchQuery"
                placeholder="搜尋產品名稱..."
                @ionInput="handleProductSearch"
                show-clear-button="focus"
              ></ion-searchbar>

              <div class="filter-row">
                <ion-select
                  v-model="selectedProductCategory"
                  placeholder="產品類別"
                  interface="popover"
                  @ionChange="applyProductFilters"
                  class="category-filter"
                >
                  <ion-select-option value="">全部類別</ion-select-option>
                  <ion-select-option
                    v-for="category in productCategories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.title }}
                  </ion-select-option>
                </ion-select>

                <ion-select
                  v-model="selectedStockStatus"
                  placeholder="庫存狀態"
                  interface="popover"
                  @ionChange="applyProductFilters"
                  class="stock-filter"
                >
                  <ion-select-option value="">全部狀態</ion-select-option>
                  <ion-select-option value="in_stock">有貨</ion-select-option>
                  <ion-select-option value="out_of_stock">缺貨</ion-select-option>
                </ion-select>
              </div>

              <!-- Results Count -->
              <div class="results-info">
                <span class="results-count">
                  顯示 {{ filteredProducts.length }} / {{ products.length }} 個產品
                </span>
                <ion-button
                  v-if="hasActiveFilters"
                  fill="clear"
                  size="small"
                  @click="clearFilters"
                >
                  清除篩選
                </ion-button>
              </div>
            </div>

            <!-- Mode Indicator -->
            <div class="mode-indicator">
              <ion-chip :color="productManagementMode === 'manage' ? 'primary' : 'secondary'">
                <ion-icon :icon="productManagementMode === 'manage' ? searchOutline : reorderThreeOutline"></ion-icon>
                <ion-label>
                  {{ productManagementMode === 'manage' ? '管理模式：可搜尋和篩選' : '排序模式：可拖拽重新排序' }}
                </ion-label>
              </ion-chip>
            </div>
          </div>

          <!-- Grid View for Visitors -->
          <div v-if="!isOwner" class="products-grid">
            <template v-if="products.length > 0">
              <ion-card
                v-for="product in products"
                :key="product.id"
                class="product-card"
                button
                @click="previewProduct(product)"
              >
                <div class="product-image">
                  <img :src="product.cover_image" :alt="product.title">
                  <ion-badge
                    :color="product.is_in_stock ? 'success' : 'medium'"
                    class="stock-badge"
                  >
                    {{ product.is_in_stock ? '有貨' : '缺貨' }}
                  </ion-badge>
                </div>
                <ion-card-header>
                  <ion-card-title>{{ product.title }}</ion-card-title>
                  <ion-card-subtitle>HK$ {{ product.price }}</ion-card-subtitle>
                </ion-card-header>
              </ion-card>
            </template>

            <!-- Empty State for Visitors -->
            <div v-else class="empty-products-state">
              <ion-icon :icon="cubeOutline" color="medium"></ion-icon>
              <p>此商店暫無產品</p>
            </div>
          </div>

          <!-- List View for Shop Owner -->
          <ion-list v-else>
            <ion-reorder-group
              :disabled="productManagementMode === 'manage'"
              @ionItemReorder="handleReorder($event)"
            >
              <ion-item v-for="product in displayedProducts" :key="product.id" class="product-item" lines="full">
                <ion-reorder slot="start"></ion-reorder>
                <ion-thumbnail slot="start" @click.stop="previewProduct(product)">
                  <img :src="product.cover_image" :alt="product.title" class="square-thumbnail">
                </ion-thumbnail>
                <ion-label>
                  <h2>{{ product.title }}</h2>
                  <p class="price">HK$ {{ product.price }}</p>
                  <ion-badge slot="end" :color="product.is_in_stock ? 'success' : 'medium'">
                    {{ product.is_in_stock ? '有貨' : '缺貨' }}
                  </ion-badge>
                </ion-label>
                <div class="product-actions" slot="end" style="flex-direction: column;">
                  <ion-button
                    fill="clear"
                    @click.stop="previewProduct(product)"
                    title="預覽產品"
                  >
                    <ion-icon :icon="eyeOutline" color="primary"></ion-icon>
                  </ion-button>
                  <ion-button
                    fill="clear"
                    :router-link="`/shops/${shop.id}/products/${product.id}/edit`"
                  >
                    <ion-icon :icon="pencilOutline" color="primary"></ion-icon>
                  </ion-button>
                  <ion-button
                    fill="clear"
                    @click.stop="confirmDelete(product)"
                  >
                    <ion-icon :icon="trashOutline" color="danger"></ion-icon>
                  </ion-button>
                </div>
              </ion-item>
            </ion-reorder-group>

            <!-- Empty State for Shop Owner -->
            <div v-if="products.length === 0" class="empty-products-state owner-empty-state">
              <ion-icon :icon="cubeOutline" color="medium"></ion-icon>
              <p>您還沒有新增任何產品</p>
              <ion-button
                :router-link="`/shops/${shop.id}/products/new`"
                expand="block"
                class="add-first-product-btn"
              >
                <ion-icon :icon="addOutline" slot="start"></ion-icon>
                新增第一個產品
              </ion-button>
            </div>
          </ion-list>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到商店</p>
      </div>

      <!-- Shop Edit Modal -->
      <ShopFormModal
        :is-open="showEditModal"
        :shop="shop"
        @close="showEditModal = false"
        @updated="handleShopUpdated"
      />

      <!-- Delete Confirmation Alert -->
      <ion-alert
        :is-open="!!productToDelete"
        header="確認刪除"
        :message="`確定要刪除產品「${productToDelete?.title}」嗎？`"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { productToDelete = null; }
          },
          {
            text: '刪除',
            role: 'destructive',
            handler: () => handleDeleteProduct()
          }
        ]"
      ></ion-alert>

      <!-- Preview Mode Alert -->
      <ion-alert
        :is-open="showPreviewAlert"
        header="預覽模式"
        message="您正在以訪客身份預覽商店。點擊左上角「退出預覽」返回管理模式。"
        :buttons="[
          {
            text: '確定',
            handler: () => { showPreviewAlert = false; }
          }
        ]"
      ></ion-alert>

      <!-- Rating Modal -->
      <ion-modal :is-open="showRatingModal" @didDismiss="showRatingModal = false">
        <div class="rating-modal-content">
          <h2>評分商店</h2>
          <p>請為 {{ shop?.name }} 評分</p>

          <div class="rating-stars">
            <star-rating
              v-model:rating="userRating"
              :increment="1"
              :star-size="40"
              :show-rating="false"
              :animate="true"
              active-color="#ffd055"
            ></star-rating>
          </div>

          <div class="rating-actions">
            <ion-button fill="outline" @click="showRatingModal = false">取消</ion-button>
            <ion-button @click="submitRating">提交</ion-button>
          </div>
        </div>
      </ion-modal>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonThumbnail,
  IonBadge,
  IonSpinner,
  IonToast,
  IonAlert,
  IonReorderGroup,
  IonReorder,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,

  IonModal,
  IonChip,
  IonSegment,
  IonSegmentButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  createOutline,
  addOutline,
  alertCircleOutline,
  pencilOutline,
  trashOutline,
  eyeOutline,
  closeCircleOutline,
  star,
  cubeOutline,
  heart,
  heartOutline,
  time,
  cube,
  pricetag,
  personOutline,
  chevronForwardOutline,
  globeOutline,
  openOutline,
  documentTextOutline,
  businessOutline,
  callOutline,
  mailOutline,
  locationOutline,

  logoFacebook,
  logoInstagram,
  logoLinkedin,
  ellipsisHorizontalOutline,
  searchOutline,
  reorderThreeOutline,
} from 'ionicons/icons';
import StarRating from 'vue-star-rating';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import ShopFormModal from '@/components/ShopFormModal.vue';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const shopId = route.params.shopId as string;

const shop = ref<any>(null);
const products = ref<any[]>([]);
const isLoading = ref(true);
const showEditModal = ref(false);
const toastMessage = ref('');
const productToDelete = ref<any>(null);
const showPreviewAlert = ref(false);
const isPreviewMode = ref(false);
const showRatingModal = ref(false);
const userRating = ref(0);
const userHasRated = ref(false);
const userRatingId = ref<string | null>(null);
const isShopLiked = ref(false);
const shopCategory = ref<any>(null);
const selectedProfileTab = ref('description');

// Product management state
const productManagementMode = ref<'manage' | 'reorder'>('manage');
const productSearchQuery = ref('');
const selectedProductCategory = ref<number | null>(null);
const selectedStockStatus = ref<string>('');
const productCategories = ref<any[]>([]);

const isOwner = computed(() => {
  if (isPreviewMode.value) return false;
  return shop.value?.owner_id === authStore.currentUser?.id;
});

const hasProfileData = computed(() => {
  if (!shop.value) return false;

  return !!(
    shop.value.description ||
    shop.value.website ||
    shop.value.business_type ||
    shop.value.main_products_services?.length ||
    shop.value.service_area ||
    shop.value.contact_phone ||
    shop.value.contact_email ||
    shop.value.physical_address ||
    shop.value.operating_hours ||
    shop.value.languages_supported?.length ||
    shop.value.payment_methods?.length ||
    shop.value.keywords?.length ||
    shop.value.social_media_links
  );
});

// Product filtering computed properties
const filteredProducts = computed(() => {
  let filtered = [...products.value];

  // Apply search filter
  if (productSearchQuery.value) {
    const query = productSearchQuery.value.toLowerCase();
    filtered = filtered.filter(product =>
      product.title.toLowerCase().includes(query) ||
      (product.description && product.description.toLowerCase().includes(query))
    );
  }

  // Apply category filter
  if (selectedProductCategory.value) {
    filtered = filtered.filter(product =>
      product.category_id === selectedProductCategory.value
    );
  }

  // Apply stock status filter
  if (selectedStockStatus.value) {
    if (selectedStockStatus.value === 'in_stock') {
      filtered = filtered.filter(product => product.is_in_stock);
    } else if (selectedStockStatus.value === 'out_of_stock') {
      filtered = filtered.filter(product => !product.is_in_stock);
    }
  }

  return filtered;
});

const displayedProducts = computed(() => {
  return productManagementMode.value === 'manage' ? filteredProducts.value : products.value;
});

const hasActiveFilters = computed(() => {
  return !!(
    productSearchQuery.value ||
    selectedProductCategory.value ||
    selectedStockStatus.value
  );
});

const formatJoinDate = (dateString: string) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天加入';
  } else if (diffDays < 7) {
    return `${diffDays}天前加入`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}週前加入`;
  } else if (diffDays < 365) {
    return `${Math.floor(diffDays / 30)}個月前加入`;
  } else {
    return `${Math.floor(diffDays / 365)}年前加入`;
  }
};

const loadShopDetails = async () => {
  try {
    const { data: shopData, error: shopError } = await supabase
      .from('shops')
      .select(`
        *,
        shop_categories:shop_category_id(*),
        owner:owner_id (
          full_name
        )
      `)
      .eq('id', shopId)
      .single();

    if (shopError) throw shopError;
    shop.value = shopData;
    shopCategory.value = shopData.shop_categories;

    // Check if user has already rated this shop
    if (authStore.isAuthenticated && authStore.currentUser?.id) {
      const { data: ratingData, error: ratingError } = await supabase
        .from('shop_ratings')
        .select('*')
        .eq('user_id', authStore.currentUser.id)
        .eq('shop_id', shopId)
        .single();

      if (!ratingError && ratingData) {
        userHasRated.value = true;
        userRating.value = ratingData.rating;
        userRatingId.value = ratingData.id;
      }

      // Check if user has liked this shop using the userStore
      isShopLiked.value = userStore.isShopLiked(shopId);
    }
  } catch (error) {
    console.error('Error fetching shop details:', error);
    toastMessage.value = '載入商店資料時發生錯誤';
  }
};

const loadProducts = async () => {
  try {
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select('*')
      .eq('shop_id', shopId)
      .eq('status', 'active')
      .order('display_order', { ascending: true });

    if (productsError) throw productsError;
    products.value = productsData;
  } catch (error) {
    console.error('Error fetching products:', error);
    toastMessage.value = '載入產品資料時發生錯誤';
  }
};

const loadProductCategories = async () => {
  try {
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('id', { ascending: true });

    if (categoriesError) throw categoriesError;
    productCategories.value = categoriesData || [];
  } catch (error) {
    console.error('Error fetching product categories:', error);
  }
};

// Use Ionic lifecycle hooks
onIonViewDidEnter(async () => {
  isLoading.value = true;
  await loadShopDetails();
  await loadProducts();
  await loadProductCategories();
  isLoading.value = false;
});

const handleShopUpdated = (updatedShop: any) => {
  shop.value = updatedShop;
  showEditModal.value = false;
  toastMessage.value = '商店資料已更新';
  loadShopDetails();
};

const confirmDelete = (product: any) => {
  productToDelete.value = product;
};

const handleDeleteProduct = async () => {
  if (!productToDelete.value) return;

  try {
    const { error } = await supabase
      .from('products')
      .update({ status: 'trashed' })
      .eq('id', productToDelete.value.id);

    if (error) throw error;

    products.value = products.value.filter(p => p.id !== productToDelete.value.id);
    toastMessage.value = '產品已刪除';
  } catch (error) {
    console.error('Error deleting product:', error);
    toastMessage.value = '刪除產品時發生錯誤';
  } finally {
    productToDelete.value = null;
  }
};

const handleReorder = async (event: CustomEvent) => {
  const { from, to } = event.detail;

  // Update local array
  const itemMove = products.value.splice(from, 1)[0];
  products.value.splice(to, 0, itemMove);

  // Complete the animation
  event.detail.complete();

  try {
    // Update display_order for all affected products
    const updates = products.value.map((product, index) => ({
      id: product.id,
      display_order: index
    }));

    const { error } = await supabase
      .from('products')
      .upsert(updates, { onConflict: 'id' });

    if (error) throw error;
  } catch (error) {
    console.error('Error updating product order:', error);
    toastMessage.value = '更新產品順序時發生錯誤';
    // Reload products to restore original order
    await loadProducts();
  }
};

const previewProduct = (product: any) => {
  router.push(`/shops/${shopId}/products/${product.id}`);
};

// Product management functions
const handleModeChange = (event: CustomEvent) => {
  productManagementMode.value = event.detail.value;
};

const handleProductSearch = (event: CustomEvent) => {
  productSearchQuery.value = event.detail.value;
};

const applyProductFilters = () => {
  // Filters are automatically applied through computed properties
};

const clearFilters = () => {
  productSearchQuery.value = '';
  selectedProductCategory.value = null;
  selectedStockStatus.value = '';
};

const previewShop = () => {
  isPreviewMode.value = true;
  showPreviewAlert.value = true;
};

const exitPreview = () => {
  isPreviewMode.value = false;
};

const openRatingModal = () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以評分商店';
    return;
  }

  if (isOwner.value) {
    toastMessage.value = '您不能評分自己的商店';
    return;
  }

  showRatingModal.value = true;
};

const toggleFavorite = async () => {
  // Use the reusable toggleFavorite function from userStore
  const result = await userStore.toggleFavorite(shopId);

  // Display the result message
  if (result.message) {
    toastMessage.value = result.message;
  }

  // Update UI state if successful
  if (result.success) {
    // Update local state
    isShopLiked.value = userStore.isShopLiked(shopId);

    // Update like count in UI
    if (isShopLiked.value) {
      // Just added to favorites
      shop.value.like_count = (shop.value.like_count || 0) + 1;
    } else {
      // Just removed from favorites
      if (shop.value.like_count && shop.value.like_count > 0) {
        shop.value.like_count -= 1;
      }
    }
  }
};

const submitRating = async () => {
  if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
    toastMessage.value = '請先登入以評分商店';
    return;
  }

  try {
    if (userHasRated.value && userRatingId.value) {
      // Update existing rating
      const { error } = await supabase
        .from('shop_ratings')
        .update({
          rating: userRating.value
        })
        .eq('id', userRatingId.value);

      if (error) throw error;
      toastMessage.value = '評分已更新';
    } else {
      // Create new rating
      const { data, error } = await supabase
        .from('shop_ratings')
        .insert({
          user_id: authStore.currentUser.id,
          shop_id: shopId,
          rating: userRating.value
        })
        .select()
        .single();

      if (error) throw error;
      userRatingId.value = data.id;
      userHasRated.value = true;
      toastMessage.value = '評分已提交';
    }

    // Update shop rating in UI
    const { data: updatedShop, error: shopError } = await supabase
      .from('shops')
      .select('rating, rating_count')
      .eq('id', shopId)
      .single();

    if (!shopError && updatedShop) {
      shop.value.rating = updatedShop.rating;
      shop.value.rating_count = updatedShop.rating_count;
    }

    showRatingModal.value = false;
  } catch (error) {
    console.error('Error submitting rating:', error);
    toastMessage.value = '提交評分時發生錯誤';
  }
};

// Navigate to user profile
const navigateToUserProfile = (userId: string) => {
  if (!userId) return;

  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以查看用戶資料';
    router.push('/login');
    return;
  }

  // Check if user has permission to view profiles (merchant or president only)
  if (authStore.currentUser?.role === 'free') {
    toastMessage.value = '升級會員以查看用戶資料';
    return;
  }

  // Navigate to user profile
  router.push(`/users/${userId}`);
};

// Format website URL for display
const formatWebsiteUrl = (url: string) => {
  if (!url) return '';

  // Remove protocol for display
  return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
};

// Open website in new tab
const openWebsite = (url: string) => {
  if (!url) return;

  // Ensure URL has protocol
  const websiteUrl = url.startsWith('http') ? url : `https://${url}`;

  // Open in new tab
  window.open(websiteUrl, '_blank', 'noopener,noreferrer');
};

// Open social media link in new tab
const openLink = (url: string) => {
  if (!url) return;

  // Ensure URL has protocol
  const linkUrl = url.startsWith('http') ? url : `https://${url}`;

  // Open in new tab
  window.open(linkUrl, '_blank', 'noopener,noreferrer');
};
</script>

<style scoped>
.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.shop-container {
  max-width: 1200px;
  margin: 0 auto;
}

.shop-banner {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shop-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.shop-logo {
  width: 100px;
  height: 100px;
  border-radius: 20px;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  background-color: white;
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.shop-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.shop-info-section {
  background: white;
  border-radius: 0 0 24px 24px;
  padding: 1.5rem 2rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shop-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.shop-title-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.shop-name {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: var(--ion-color-dark);
}

.shop-actions-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.25rem;
  flex-wrap: wrap;
}

.shop-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--ion-color-light);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.shop-rating ion-icon {
  color: var(--ion-color-warning);
  font-size: 1.1rem;
}

.rate-button, .like-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --border-radius: 20px;
  --padding-start: 0.75rem;
  --padding-end: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  height: 32px;
}

.shop-metrics-section {
  margin-bottom: 1.5rem;
}

.shop-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--ion-color-light-shade);
  border-radius: 12px;
  padding: 0.75rem 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.shop-category ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.2rem;
}

.shop-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ion-color-light);
  border-radius: 12px;
  padding: 0.75rem 1.25rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.metric-item ion-icon {
  font-size: 1.2rem;
  color: var(--ion-color-medium);
}

.metric-divider {
  width: 1px;
  height: 24px;
  background: var(--ion-color-medium-tint);
  opacity: 0.5;
}

.shop-owner-section {
  margin-bottom: 1.5rem;
}

.shop-owner-item {
  --background: var(--ion-color-light);
  --border-radius: 12px;
  --padding-start: 1.25rem;
  --padding-end: 1.25rem;
  --padding-top: 0.75rem;
  --padding-bottom: 0.75rem;
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.shop-owner-item:hover {
  --background: var(--ion-color-light-shade);
}

.owner-icon {
  color: var(--ion-color-primary);
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.shop-owner-item ion-label h3 {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.shop-owner-item ion-label h2 {
  font-size: 1.1rem;
  color: var(--ion-color-dark);
  margin: 0;
  font-weight: 600;
}

.shop-website-section {
  margin-bottom: 1.5rem;
}

.shop-website-item {
  --background: var(--ion-color-light);
  --border-radius: 12px;
  --padding-start: 1.25rem;
  --padding-end: 1.25rem;
  --padding-top: 0rem;
  --padding-bottom: 0rem;
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.shop-website-item:hover {
  --background: var(--ion-color-light-shade);
}

.website-icon {
  color: var(--ion-color-primary);
  font-size: 1.3rem;
  margin-right: 0.5rem;
}

.shop-website-item ion-label h3 {
  font-size: 0.7rem;
  color: var(--ion-color-medium);
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.shop-website-item ion-label h2 {
  font-size: 0.9rem;
  color: var(--ion-color-primary);
  margin: 0;
  font-weight: 600;
  text-decoration: underline;
}

.empty-products-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background: var(--ion-color-light);
  border-radius: 16px;
  text-align: center;
  width: 100%;
}

.empty-products-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
  color: var(--ion-color-medium);
}

.empty-products-state p {
  font-size: 1.1rem;
  color: var(--ion-color-medium);
  margin: 0 0 1.5rem;
}

.owner-empty-state {
  padding: 4rem 2rem;
}

.add-first-product-btn {
  max-width: 250px;
  margin-top: 1rem;
}

.rating-modal-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.rating-modal-content h2 {
  margin-top: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.rating-stars {
  margin: 2rem 0;
}

.rating-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-dark);
}

.add-product-btn {
  --color: var(--ion-color-primary);
  font-weight: 600;
}

/* Product Management Controls */
.product-controls {
  margin-bottom: 1.5rem;
  background: white;
  border-radius: 16px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mode-toggle {
  margin-bottom: 1rem;
}

.mode-toggle ion-segment {
  --background: var(--ion-color-light);
}

.mode-toggle ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary);
  --background-checked: white;
  --border-radius: 8px;
  min-height: 40px;
}

.mode-toggle ion-label {
  font-weight: 500;
  margin-right: 0.5rem;
}

.search-filters {
  margin-bottom: 1rem;
}

.search-filters ion-searchbar {
  --background: var(--ion-color-light);
  --border-radius: 12px;
  margin-bottom: 0.75rem;
}

.filter-row {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.category-filter,
.stock-filter {
  flex: 1;
  --background: var(--ion-color-light);
  --border-radius: 12px;
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-top: 1px solid var(--ion-color-light);
}

.results-count {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.mode-indicator {
  text-align: center;
}

.mode-indicator ion-chip {
  --background: var(--ion-color-light);
  --color: var(--ion-color-dark);
}

.mode-indicator ion-chip ion-icon {
  margin-right: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .results-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.product-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  padding-top: 100%;
}

.product-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stock-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
}

ion-card-header {
  padding: 1rem;
}

ion-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

ion-card-subtitle {
  font-size: 1.1rem;
  color: var(--ion-color-primary);
  font-weight: 600;
}

.product-item {
  --padding-start: 1rem;
  --padding-end: 1rem;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  --border-radius: 12px;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

ion-thumbnail {
  --size: 80px;
  margin-right: 1rem;
}

ion-thumbnail img {
  border-radius: 8px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.product-actions ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  height: 36px;
}

.product-actions ion-icon {
  font-size: 1.2rem;
}

ion-reorder {
  cursor: grab;
}

ion-reorder ion-icon {
  color: var(--ion-color-medium);
}

.preview-exit {
  --color: var(--ion-color-primary);
  font-weight: 500;
}

@media (max-width: 768px) {
  .shop-banner {
    height: 180px;
    border-radius: 16px 16px 0 0;
  }

  .shop-logo {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    margin-right: 1rem;
  }

  .shop-info-section {
    padding: 1rem 1.25rem 1.5rem;
    border-radius: 0 0 16px 16px;
  }

  .shop-header {
    margin-bottom: 1rem;
  }

  .shop-name {
    font-size: 1.4rem;
  }

  .shop-actions-container {
    margin-top: 0.5rem;
  }

  .shop-rating {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
  }

  .rate-button, .like-button {
    font-size: 0.8rem;
    --padding-start: 0.5rem;
    --padding-end: 0.5rem;
    height: 28px;
  }

  .shop-category {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .shop-category ion-icon {
    font-size: 1rem;
  }

  .shop-metrics {
    padding: 0.5rem 0.75rem;
  }

  .metric-item {
    font-size: 0.8rem;
  }

  .metric-item ion-icon {
    font-size: 1rem;
  }

  .section-subtitle {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .shop-description {
    font-size: 0.9rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 1rem;
  }

  .empty-products-state {
    padding: 2rem 1rem;
  }

  .empty-products-state ion-icon {
    font-size: 36px;
  }

  .empty-products-state p {
    font-size: 1rem;
  }

  .owner-empty-state {
    padding: 2.5rem 1.5rem;
  }

  .shop-owner-section {
    margin-bottom: 1rem;
  }

  .shop-owner-item {
    --padding-start: 1rem;
    --padding-end: 1rem;
    --padding-top: 0.5rem;
    --padding-bottom: 0.5rem;
  }

  .owner-icon {
    font-size: 1.3rem;
  }

  .shop-owner-item ion-label h3 {
    font-size: 0.8rem;
  }

  .shop-owner-item ion-label h2 {
    font-size: 1rem;
  }
}

/* Shop Profile Section Styles */
.shop-profile-section {
  margin-bottom: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.section-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin: 0;
}

.edit-profile-btn {
  --color: var(--ion-color-primary);
  font-weight: 500;
  font-size: 0.85rem;
}

.profile-tabs-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.profile-segment {
  --background: var(--ion-color-light);
  margin: 0;
}

.profile-segment ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary);
  --background-checked: white;
  --indicator-color: var(--ion-color-primary);
  font-size: 0.8rem;
  min-height: 48px;
}

.profile-segment ion-segment-button ion-icon {
  font-size: 1rem;
  margin-bottom: 2px;
}

.tab-content {
  padding: 1rem;
}

.tab-panel {
  min-height: 120px;
}

.description-content {
  line-height: 1.6;
}

.description-text {
  font-size: 0.95rem;
  color: var(--ion-color-dark);
  margin: 0 0 1rem 0;
  white-space: pre-wrap;
}

.no-description {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  font-style: italic;
  margin: 0 0 1rem 0;
}

.website-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ion-color-light-shade);
}

.website-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
}

.website-item ion-icon:first-child {
  color: var(--ion-color-primary);
  font-size: 1rem;
  flex-shrink: 0;
}

.website-label {
  color: var(--ion-color-medium);
  font-weight: 500;
  min-width: 40px;
}

.website-link {
  color: var(--ion-color-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.website-link:hover {
  text-decoration: underline;
}

.external-icon {
  font-size: 0.8rem;
  opacity: 0.7;
}

.profile-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.profile-item-compact {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.profile-item-compact .label {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.profile-item-compact .value {
  font-size: 0.9rem;
  color: var(--ion-color-dark);
  font-weight: 400;
  line-height: 1.3;
}

.products-section, .keywords-section {
  margin-bottom: 1rem;
}

.products-section:last-child, .keywords-section:last-child {
  margin-bottom: 0;
}

.products-section h4, .keywords-section h4 {
  font-size: 0.85rem;
  color: var(--ion-color-medium);
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.tags-container-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
}

.tags-container-compact ion-chip {
  font-size: 0.8rem;
  height: 28px;
}

.contact-info-compact {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item-compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--ion-color-dark);
  font-size: 0.9rem;
}

.contact-item-compact ion-icon {
  color: var(--ion-color-primary);
  font-size: 1rem;
  flex-shrink: 0;
}

.support-section-compact {
  margin-bottom: 1rem;
}

.support-section-compact:last-child {
  margin-bottom: 0;
}

.support-section-compact h4 {
  font-size: 0.85rem;
  color: var(--ion-color-medium);
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.social-section {
  margin-top: 1rem;
}

.social-section h4 {
  font-size: 0.85rem;
  color: var(--ion-color-medium);
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.social-links-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.social-links-compact ion-button {
  --border-radius: 16px;
  font-size: 0.8rem;
  height: 32px;
}

.profile-empty-state-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: var(--ion-color-light);
  border-radius: 12px;
  text-align: center;
}

.profile-empty-state-compact ion-icon {
  font-size: 32px;
  margin-bottom: 0.5rem;
  color: var(--ion-color-medium);
}

.profile-empty-state-compact p {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  margin: 0;
  line-height: 1.3;
}

@media (max-width: 768px) {
  .profile-grid-compact {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .profile-segment ion-segment-button {
    font-size: 0.75rem;
    min-height: 44px;
  }

  .profile-segment ion-segment-button ion-icon {
    font-size: 0.9rem;
  }

  .tab-content {
    padding: 0.75rem;
  }

  .social-links-compact {
    justify-content: center;
  }
}


</style>